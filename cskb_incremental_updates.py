from config import cskb_agent_info
from 


# 获取CSKB增量变化，用以进行向量更新
def fetch_cskb_incremental_updates(knowledge_field:str, type: str):
    # import pandas as pd
    # import os

    # new_original_ids = []       # CSKB中新增的文档
    # update_original_ids = []    # CSKB中被更新的文档
    # delete_original_ids = []    # CSKB中被删除的文档
    # original_ids_to_delete = []        # 向量库中需要删除的文档
    # docs_to_insert = []                # 向量库中需要补充的文档

    # # # 读取当前向量库文档列表
    # # chunks_info_path = os.path.join("embeddings", knowledge_field)
    # # if type == "doc":
    # #     chunks_info_file = os.path.join(chunks_info_path, "chunks_info.xlsx")
    # # elif type == "faq":
    # #     chunks_info_path = os.path.join(chunks_info_path, "FAQ")
    # #     chunks_info_file = os.path.join(chunks_info_path, "chunks_info.xlsx")
    # # else:
    # #     print (f"Error: 获取CSKB增量变化，未知的知识库类型{type}")
    # #     return [], []

    # # 读取当前向量index文件的original_id清单和doc_id清单
    # current_original_ids, current_ids = [], []
    # current_index = load_vector_index(knowledge_field=knowledge_field, type=type)
    # if current_index is None:
    #     return [], []
    # current_index_dict = current_index.docstore._dict
    # for doc_originial_id, doc_info in current_index_dict.items():
    #     original_id = doc_info.metadata['source_id']
    #     doc_id = doc_info.metadata['doc_id']
    #     if original_id not in current_original_ids: current_original_ids.append(original_id)
    #     if doc_id not in current_ids: current_ids.append(doc_id)

    # # 读取当前向量库文档original_id列表和doc_id列表
    # current_df = pd.read_excel(chunks_info_file)
    # current_list = current_df[current_df['doc_type'] == '文档'][['doc_original_id', 'doc_id']].to_dict('records')
    # current_original_ids_2 = [item['doc_original_id'] for item in current_list]
    # current_ids_2 = [item['doc_id'] for item in current_list]
    latest_original_ids = []           # CSKB中最新的文档清单

    # 从这里开始往下的代码，旨在获取CSKB中的增量更新，并返回增量变化结果
    cskb_agents = cskb_agent_info(knowledge_field)

    for agent in cskb_agents:
        load_attach = agent['load_attach']
        
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": agent['token'],
            "X-USER-ID": agent['user_id']
        }
        
        # 根据入参type类型，判断取文档还是FAQ
        if type == "doc":
            latest_list = fetch_all_docs(knowledge_field=knowledge_field, headers=headers)
            latest_list_faq = fetch_all_faq(knowledge_field=knowledge_field, headers=headers)
            latest_list.extend(latest_list_faq)
        else:
            latest_list = fetch_all_faq(knowledge_field=knowledge_field, headers=headers)
        
        latest_original_ids_for_agent = [item['originalId'] for item in latest_list]
        latest_original_ids.extend(latest_original_ids_for_agent)
        latest_ids = [item['id'] for item in latest_list]

        # [Part 1] 获取新增文档清单，处理逻辑为：判断新老两个doc_id列表的差异部分，新列表中，老列表中没有的即为新增文档。
        new_ids = list(set(latest_ids) - set(current_ids))
        
        # [Part 2] 获取更新文档清单，处理逻辑为：取latest_doc_ids和current_doc_ids的交集（即存在同一篇文档），再判断是否有更新。
        same_ids = list(set(latest_ids) & set(current_ids))
        update_ids = []     # 用于记录当前agent中的更新文档的doc_id
        for id in same_ids:
            latest_id = latest_ids[latest_ids.index(id)]
            current_id = current_ids[current_ids.index(id)]
            if latest_id != current_id:
                update_ids.append(id)
        # 同时，每个Agent下更新文档的doc_id，需要追加到update_doc_ids列表，并在循环体结束后和delete_doc_ids合并处理。以便在向量库中和删除文档清单一起对旧向量进行删除
        update_original_ids.extend(update_ids)

        # CSKB中新增和更新的文档的doc_id，在后续代码中获取具体文档内容，在向量库中补充（更新的文档在上一步删除后重新补充覆盖）
        ids_to_insert = new_ids + update_ids

        # 获取更新文档和新增文档的具体内容，返回给上游函数
        dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)
        for doc_id in ids_to_insert:
            doc_detail = doc_detail_query(knowledge_field=knowledge_field, headers=headers, agent_dict=None, doc_id=doc_id)
            if doc_detail is None:
                continue

            # 补充完整目录名称和目录层级
            doc_dir_id = doc_detail['dirId']
            doc_detail['dir_name'] = f"[{doc_detail['agentName']}] {dir_tree_dict[doc_dir_id]['full_name']}"
            doc_detail['dir_level'] = dir_tree_dict[doc_dir_id]['level']
            docs_to_insert.append({"type": "doc", "data": doc_detail})
            
            # 如需同时更新附件，再通过以下代码获取和补充附件信息
            if load_attach is True:
                doc_attaches = doc_detail['attaches']
                if doc_attaches is None:
                    continue

                for attach in doc_attaches:
                    # 附件文档中部分key，用其他key值替换
                    attach['originalId'] = attach['id']
                    attach['title'] = attach['name']

                    # 附件的基本信息和其关联主文档保持一致，TODO：今后若要区分层级再考虑修改此部分代码
                    attach['agentId'] = doc_detail['agentId']
                    attach['agentName'] = doc_detail['agentName']
                    attach['dirId'] = doc_detail['dirId']
                    attach['dir_name'] = doc_detail['dir_name']
                    attach['dir_level'] = doc_detail['dir_level']

                    attach_text = attach_download(knowledge_field=knowledge_field, headers=headers, doc_title=doc_detail['title'], attach_title = attach['title'], attach_id=attach['previewId'])
                    attach['docText'] = attach_text

                    docs_to_insert.append({"type": "attach", "data": attach})


    # [Part 3] 获取删除文档original_ids清单。处理逻辑为：判断老列表中的original_id，新列表中没有的即为删除文档。同时，更新文档的original_id，需要在向量库中将原有向量先删除，所以需要合并。
    delete_original_ids = list(set(current_original_ids) - set(latest_original_ids))
    original_ids_to_delete = delete_original_ids + update_original_ids     # CSKB中删除和更新的文档的original_id，都需要从向量库中根据id值直接删除

    return original_ids_to_delete, docs_to_insert