import re
import json
import fitz
import requests
from bs4 import BeautifulSoup
from tqdm import tqdm

# from knowledge_process import chunk_base_faq, chunk_base_doc
from config import KNOWLEDGE_FIELD_CSKB_ENV, CSKB_ADDRESS_DICT, cskb_agent_info, DOC_EMBEDDING_END_OFFSET, DOC_EMBEDDING_START_OFFSET


print_mode = "simplify"
all_dir_tree_dict = {}


# 获取所有文档
def fetch_all_docs(knowledge_field, headers):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']

    finish_fetching = False
    current_round = 0
    max_round = 9999

    docs_list = []
    pn = 1 # 页码
    
    while (finish_fetching is False):
        doc_list_url = f"{cskb_url}/cskb/open/api/v1/doc?pn={pn}&ps=100"
        response = requests.get(doc_list_url, headers=headers)
        answer_json = response.json() 

        if "data" not in answer_json.keys():
            continue

        answer = answer_json['data']['list']

        for ans in answer:
            doc_info = {
                "id": ans['id'],
                "originalId": ans['originalId'],
            }
            docs_list.append(doc_info)

        current_round += 1
        pn += 1
        if (len(answer) == 0 or current_round > max_round):
            # 如果返回结果为空或者超过最大查询次数，则结束查询
            finish_fetching = True

    return docs_list

# 查询知识库文档具体内容
def doc_detail_query(knowledge_field, headers, agent_dict, doc_id):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']
    
    doc_detail_url = f"{cskb_url}/cskb/open/api/v1/doc/detail?id={doc_id}"

    response = requests.get(doc_detail_url, headers=headers)

    answer_json = response.json()

    if "data" not in answer_json.keys():
        return None

    data = answer_json['data']
    html_answer = data['richText']

    # 使用BeautifulSoup解析HTML文档
    soup = BeautifulSoup(html_answer, 'html.parser')

    # for item in soup.find_all('img'):
    #     img_path = item.get('src')
    #     img_src = img_path.split('&')[0]
    #     img_id = img_path.split('&')[1]
    #     img_id = img_id.split('=')[1]
    #     img_path = img_src_share_prefix + img_src + f"&token={kb_token}&agentid=1{agent_id}&id={img_id}"

    #     html_answer = html_answer.replace(img_src, img_path)

    # 定位要删除的DOM节点
    [s.extract() for s in soup("title")]
    text_answer = soup.get_text().strip()

    # 根据agent_id获取agent_name
    if agent_dict is None:
        # 如果agent_dict为空未传入，则重新获取agent_dict。这种情况一般发生在增量embedding过程中
        agent_dict = fetch_agent_dict(knowledge_field=knowledge_field, headers=headers)
    agent_name = agent_dict[data['agentId']]

    doc_info = {
        "id": data['id'],
        "id_parent": "",
        "title": data['title'],
        "agentId": data['agentId'],
        "agentName": agent_name,
        "dirId": data['dirId'],
        "originalId": data['originalId'],
        "originalId_parent": "",
        "pubUserName": data['pubUserName'],
        "pubTime": data['pubTime'],
        "version": data['version'],
        "tags": data['tags'],
        "attaches": data['attaches'],
        "wordNum": data['wordNum'],
        "pictureNum": data['pictureNum'],
        "linkNum": data['linkNum'],
        "docText": text_answer,
        "docHtml": html_answer,
    }

    # 补充目录名称和目录层级信息
    if 'dir_name' not in doc_info.keys(): 
        dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)
        doc_dir_id = doc_info['dirId']
        doc_info['dir_name'] = f"[{doc_info['agentName']}] {dir_tree_dict[doc_dir_id]['full_name']}"
        doc_info['dir_level'] = dir_tree_dict[doc_dir_id]['level']

    return doc_info


# 查询知识库FAQ具体内容
def faq_detail_query(knowledge_field, headers, agent_dict, faq_id):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']
    
    doc_detail_url = f"{cskb_url}/cskb/open/api/v1/faq/standard/detail?id={faq_id}"

    response = requests.get(doc_detail_url, headers=headers)

    answer_json = response.json()

    if "data" not in answer_json.keys():
        return "", "", []

    ans = answer_json['data']

    # 根据agent_id获取agent_name
    if agent_dict is None:
        # 如果agent_dict为空未传入，则重新获取agent_dict。这种情况一般发生在增量embedding过程中
        agent_dict = fetch_agent_dict(knowledge_field=knowledge_field, headers=headers)
    agent_name = agent_dict[ans['agentId']]

    faq_info = {
        "id": ans['id'],
        "agentId": ans['agentId'],
        "agentName": agent_name,
        "question": ans['question'],
        "answer": ans['answerPlainText'],
        "dirId": ans['dirId'],
        "originalId": ans['originalId'],
        "pubUserName": ans['pubUserName'],
        "pubTime": ans['pubTime'],
        "versionStatus": ans['versionStatus'],
    }

    # 补充目录名称和目录层级信息
    if 'dir_name' not in faq_info.keys(): 
        dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)
        faq_dir_id = faq_info['dirId']
        faq_info['dir_name'] = f"[{faq_info['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
        faq_info['dir_level'] = dir_tree_dict[faq_dir_id]['level']

    return faq_info


def fetch_agent_dict(knowledge_field, headers):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']

    agent_name_url = f"{cskb_url}/cskb/open/api/v1/inner/core/agent?ps=50"

    response = requests.get(agent_name_url, headers=headers)

    answer = response.json()
    agentList = answer['data']['list']

    agent_dict = {}
    for agent in agentList:
        agent_id = agent['id']
        agent_name = agent['name']
        agent_dict[agent_id] = agent_name
    
    return agent_dict


# 获取目录结构
def directory_tree(knowledge_field, headers):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']

    access_token = headers['Authorization']
    if access_token in all_dir_tree_dict.keys():
        return all_dir_tree_dict[access_token]
    else:
        answer_dict = {}

        dir_tree_url = f"{cskb_url}/cskb/open/api/v1/directory?type=1"

        response = requests.get(dir_tree_url, headers=headers)
        # print(r.status_code)

        answer = response.json()
        if 'data' not in answer.keys():
            return answer_dict

        answer = answer['data']

        # 将answer中完整目录树List转化成Dictionary，Key为dir_id，Value为完整目录名
        ## 第一步：创建一个临时Dictionary，Key为dir_id，Value为{底层目录名称，父目录ID，完整目录名称（由第二步逐层补充完整）}
        temp_dir_dict = {}
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_parent_id = dir['parentId']
            temp_dir_dict[dir_id] = {"name": dir_name, "parent_id": dir_parent_id, "full_dir_name": dir_name}
        ## 第二步：逐层补充完整目录名称，并保存到一个global Dictionary中，Key为dir_id，Value为完整目录名称
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_level = dir['level']
            dir_parent_id = dir['parentId']
            dir_full_name = dir_name
            while (dir_level > 1):
                dir_parent_name = temp_dir_dict[dir_parent_id]['full_dir_name']
                dir_full_name = f"{dir_parent_name}/" + dir_full_name
                dir_parent_id = temp_dir_dict[dir_parent_id]['parent_id']
                dir_level -= 1
            
            answer_dict[dir_id] = {"name": dir_name, "full_name": dir_full_name, "level": dir['level']}
        
        all_dir_tree_dict[access_token] = answer_dict

        return answer_dict

# 获取所有FAQ
def fetch_all_faq(knowledge_field, headers):
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_url']

    faq_list = []
  
    # 获取Agent目录清单
    dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)

    # 获取Agent列表
    agent_dict = fetch_agent_dict(knowledge_field=knowledge_field, headers=headers)

    finish_fetching = False
    current_round = 0
    max_round = 999

    pn = 1 # 页码

    while (finish_fetching is False):
        standard_faq_url = f"{cskb_url}/cskb/open/api/v1/faq/standard?pn={pn}&ps=100"

        response = requests.get(standard_faq_url, headers=headers)
        answer_json = response.json()   
        
        if "data" not in answer_json.keys():
            continue

        answer = answer_json['data']['list']

        for ans in answer:
            agent_id = ans['agentId']
            agent_name = agent_dict[agent_id]

            question_text = ans['question']
            answer_json = json.loads(ans['answer'])['list'][0]
            if answer_json['type'] == 3:
                answer_text_html = answer_json['text']
                answer_text = re.sub('<.*?>', '', answer_text_html).strip()
            else:
                answer_text = answer_json['text']

            faq_info = {
                "id": ans['id'],
                "agentId": agent_id,
                "agentName": agent_name,
                "question": question_text,
                "answer": answer_text,
                "dirId": ans['dirId'],
                "originalId": ans['originalId'],
                "pubUserName": ans['pubUserName'],
                "pubTime": ans['pubTime'],
                "versionStatus": ans['versionStatus'],
            }

            faq_dir_id = faq_info['dirId']
            faq_info['dir_name'] = f"[{faq_info['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
            faq_info['dir_level'] = dir_tree_dict[faq_dir_id]['level']

            faq_list.append(faq_info)

        current_round += 1
        pn += 1
        if (len(answer) == 0 or current_round > max_round):
            # 如果返回结果为空或者超过最大查询次数，则结束查询
            finish_fetching = True

    return faq_list


# 读取附件
def attach_download(knowledge_field, headers, doc_title, attach_title, attach_id):  
    cskb_env = KNOWLEDGE_FIELD_CSKB_ENV[knowledge_field]
    cskb_attach_download_url = CSKB_ADDRESS_DICT[cskb_env]['cskb_attach_download_url']

    attach_content = f"BM/询价单名称：{doc_title}，附件名称：{attach_title}，附件内容如下：\n\n"
    
    attach_download_url = f"{cskb_attach_download_url}/cskb/storage/v1/download"
    param = {
        "id":attach_id,
        "preview": "true"
    }

    try:
        response = requests.get(attach_download_url, headers=headers, params=param)
    except Exception as error:
        print(f"附件{attach_id}下载失败: {error}")
        return ""
    finally:
        pass
    
    if response.status_code == 200:
        # 获取内容类型
        content_type = response.headers.get('Content-Type')

        # # 使用 id 作为文件名
        # file_id = param["id"]

        # 处理 PDF 响应
        if 'application/pdf' in content_type:
            try:
                pdf = fitz.open(stream=response.content, filetype="pdf")  # type: ignore

                for page in pdf:
                    text = page.get_text(sort=True)
                    text = re.sub(r"\s*\n\s*", "\n", text)
                    # @TODO:超过10000字符的部分不读取
                    if len(attach_content) > 1000000:
                        break
                    attach_content += text.strip()
            except Exception as error:
                print(f"附件{attach_id}读取失败: {error}")

            finally:
                pass
            
        # 处理图片响应
        elif 'image/' in content_type:
            # 暂时不对图片文件进行处理
            pass

            # image_format = content_type.split('/')[1]  # 获取图片格式（如 jpg, png）
            # file_name = f"{file_id}.{image_format}"
            # with open(file_name, 'wb') as image_file:
            #     image_file.write(response.content)
            # print(f"图片文件已保存为 {file_name}")

        else:
            print(f"附件id:{attach_id}, 响应内容不是 PDF 或图片")
    else:
        print(f"附件id:{attach_id}, 请求失败，状态码: {response.status_code}")

    return attach_content


def fetch_all_knowlege(knowledge_field:str, type:str):
    agent_access_token = cskb_agent_info(knowledge_field)
    # print(agent_access_token)
    # return
    
    agent_access_token = [agent for agent in agent_access_token if agent['name'] == '零号员工知识库']
    print(agent_access_token)
    # return

    # 知识库文档查询最终结果
    final_knowledge_list = []

    if (print_mode != "None"): 
        print(f'■■■ 知识库文档最终查询结果 ■■■\n')

    
    for agent in agent_access_token:
        cskb_agent_name = agent['name']
        access_token = agent['token']
        user_id = agent['user_id']

        load_attach = agent['load_attach']
        headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
            "X-USER-ID": user_id
        }

        # 获取Agent目录清单
        dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)
        print('dir_tree_dict', dir_tree_dict)
        # return
        

        all_dir_tree_dict[access_token] = dir_tree_dict
        
        # 获取Agent列表
        agent_dict = fetch_agent_dict(knowledge_field=knowledge_field, headers=headers)
        
        
        # 获取Agent FAQ清单
        faq_list = fetch_all_faq(knowledge_field=knowledge_field, headers=headers)
        
        # print('faq_list', faq_list)
        # return 


        if (print_mode != "None"): 
            print(f'\n■ {cskb_agent_name} FAQ查询结果：\n')

        for i,faq in enumerate(faq_list):
            
            # 只需要将FAQ标准问原文转化成向量，用于用户query的语义泛化匹配。选择不向量化FAQ回答，是为了提高用户query和FAQ标准问相似度匹配的精度
            question_text = faq['question']
            faq_dir_id = faq['dirId']

            # 获取完整目录名称和目录层级
            faq['dir_name'] = f"[{faq['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
            faq['dir_level'] = dir_tree_dict[faq_dir_id]['level']

            final_knowledge_list.append({"type": "faq", "data": faq})

            # 打印查询结果
            print (f"- FAQ知识{i+1}：{question_text}")
            


        if type == "doc":
            # 获取Agent文档清单
            docs_list = fetch_all_docs(knowledge_field=knowledge_field, headers=headers)
            

            if (print_mode != "None"): 
                print(f'\n■ {cskb_agent_name} 知识文档查询结果：\n')

            # 遍历查询文档及附件内容
            attach_index = 0
            for i,doc in enumerate(docs_list):
                if i < DOC_EMBEDDING_START_OFFSET:
                    continue
                elif i > DOC_EMBEDDING_END_OFFSET:
                    break
                
                doc_id = doc['id']
                doc_detail = doc_detail_query(knowledge_field=knowledge_field, headers=headers, agent_dict=agent_dict, doc_id=doc_id)
                # break

                if doc_detail is None:
                    continue

                doc_text = doc_detail['docText']
                doc_html = doc_detail['docHtml']
                doc_attaches = doc_detail['attaches']

                if (doc_text == "" and doc_html == ""):
                    continue
                
                # 将doc_detail的内容更新到doc中
                doc.update(doc_detail)

                doc_dir_id = doc['dirId']

                # 获取完整目录名称和目录层级
                doc['dir_name'] = f"[{doc['agentName']}] {dir_tree_dict[doc_dir_id]['full_name']}"
                doc['dir_level'] = dir_tree_dict[doc_dir_id]['level']
                
                # print('doc', {"type": "doc", "data": doc})
                # return
                
                final_knowledge_list.append({"type": "doc", "data": doc})

                # 打印查询结果
                print (f"- 文档知识{i+1}：[{doc['agentName']}] {doc['title']}")

                if (load_attach is True and doc_attaches is not None):
                    for attach in doc_attaches:
                        attach_index += 1

                        # 附件文档中部分key，用其他key值替换
                        attach['originalId'] = attach['id']
                        attach['title'] = attach['name']

                        # 附件的基本信息和其关联主文档保持一致，TODO：今后若要区分层级再考虑修改此部分代码
                        attach['agentId'] = doc['agentId']
                        attach['agentName'] = doc['agentName']
                        attach['dirId'] = doc['dirId']
                        attach['dir_name'] = doc['dir_name']
                        attach['dir_level'] = doc['dir_level']

                        attach_text = attach_download(knowledge_field=knowledge_field, headers=headers, doc_title=doc['title'], attach_title = attach['title'], attach_id=attach['previewId'])
                        attach['docText'] = attach_text
                        attach["id_parent"] = doc['id']

                        final_knowledge_list.append({"type": "attach", "data": attach})

                        print (f"- 附件{attach_index+1}：[{attach['agentName']}] {attach['title']}")

                
                
                break
    
    print (f'\n□ 最终获取知识库文档：{len(final_knowledge_list)}份（包含文档、附件和FAQ）\n')
    return final_knowledge_list


from knowledge_process import chunk_base_faq, chunk_base_doc

def load_and_chunk_base(knowledge_field:str):
    
    knowledge_list = fetch_all_knowlege(knowledge_field=knowledge_field, type="doc")
    # print(knowledge_list)
    # return

    for i,item in enumerate(knowledge_list):
        if (item['type'] == 'faq'):
            faq = item['data']
            chunk_base_faq(knowledge_field, faq, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk")
        elif (item['type'] == 'attach'):
            doc = item['data']
            if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
                chunk_base_doc(knowledge_field, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="attach")
            elif "[RiSE谈判助手知识库]" in doc['dir_name']:
                chunk_base_doc(knowledge_field, doc, chunk_size=30000, chunk_overlap=10000, chunk_method="Spacy", type="attach")
            else:
                chunk_base_doc(knowledge_field, doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="attach")
        else:
            # 此分支包含doc和attach两种type类型
            doc = item['data']

            if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
                chunk_base_doc(knowledge_field, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
            elif "[RiSE谈判助手知识库]" in doc['dir_name']:
                chunk_base_doc(knowledge_field, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
            else:
                chunk_base_doc(knowledge_field, doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="doc")
                break
        if (i + 1) % 10 == 0 or i + 1 == len(knowledge_list):
            print(f'Loaded Knowledge Item={i+1}/{len(knowledge_list)}, completed={round((i+1)/len(knowledge_list)*100,2)}%\n')


    return 



if __name__ == '__main__':
    knowledge_field = "EmpZero"
    type = "doc"
    # all_knowledge = fetch_all_knowlege(knowledge_field, type)
    # print(all_knowledge)
    # load_and_chunk_base(knowledge_field)
    access_token = '99f747e3-687b-4a5f-81c3-51513ea192a5'
    user_id = ''
    headers = {        
            "Content-Type": "application/json",    
            "charset": "utf-8",
            "Authorization": access_token,
            "X-USER-ID": user_id
        }
    dir_tree_dict = directory_tree(knowledge_field=knowledge_field, headers=headers)
    print(dir_tree_dict)
    