import json
import requests

from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter, SpacyTextSplitter, NLTKTextSplitter


API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

def upload_knowledge(database:str, collection:str, texts:list, metadata:dict):
    print("调用文本列表上传接口...")

    # upload_data
    upload_data = {
        "texts": texts,
        "database": database,
        "collection": collection,
        "metadata": metadata
    }
 
    # 发送请求
    try:
        response = requests.post(API_URL, json=upload_data)
        
        # 打印响应
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"成功上传 {result.get('text_count', 0)} 条文本")
            print(f"文档ID: {result.get('doc_id', '')}")
            print(f"向量维度: {result.get('vector_dimension', 0)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")
    
    print("测试完成")


# def chunk_base_faq(knowledge_field, faq_dict, chunk_size: int, chunk_method:str, chunk_overlap: int = 0):
#     faq_question = faq_dict['question']
#     print (f'Chunking faq "{faq_question}" in {chunk_method} mode')
    
#     upload_knowledge(database=knowledge_field, collection='faq', texts=[faq_question], metadata=faq_dict)


def chunk_base_faq(
    knowledge_field, faq_dict, chunk_size: int, chunk_method:str, chunk_overlap: int = 0
):
    faq_question = faq_dict['question']
    print (f'Chunking faq "{faq_question}" in {chunk_method} mode')
    
    chunked_faq = split_chunks(
        chunk_type = "cskb_faq",
        chunk_method = chunk_method,
        agent_id = faq_dict['agentId'],
        agent_name = faq_dict['agentName'],
        doc_id = faq_dict['id'],
        doc_id_parent= "",
        dir_id = faq_dict['dirId'], 
        chunk_name = faq_question,
        chunk_id = faq_dict['originalId'],
        chunk_id_parent = "",
        chunk_size = chunk_size,
        chunk_overlap = chunk_overlap,
        chunk_content = faq_dict['answer'],
        chunk_category = faq_dict['dir_name'],
    )
    
    print (f'Chunked doc "{faq_question}" in {chunk_method} mode with chunks: {len(chunked_faq)}')

    for faq in chunked_faq:
        upload_knowledge(database=knowledge_field, collection='faq', texts=[faq.page_content], metadata=faq.metadata)


# 文本分割具体步骤
def split_chunks(chunk_type, chunk_method, agent_id, agent_name, doc_id, doc_id_parent, dir_id,chunk_name, chunk_id, chunk_id_parent, chunk_size, chunk_overlap, chunk_content, chunk_category):
    chunked_docs = []

    if (chunk_type == "cskb_faq"):
        chunks = [chunk_name]
    else:
        # 重新组合chunk内容，使其同时包含知识标题和知识内容
        if (chunk_name != ""):
            new_chunk_content = "知识文档标题：" + chunk_name + "\n" + "知识文档内容：\n" + chunk_content
        else:
            new_chunk_content = chunk_content

        if chunk_method == "RCTS": 
            if str(chunk_name).find("FAQ") != -1:
                text_splitter = RecursiveCharacterTextSplitter(chunk_size = chunk_size, chunk_overlap = chunk_overlap, length_function = len, separators=['\n问题：',"\n## "])
            else:
                text_splitter = RecursiveCharacterTextSplitter(chunk_size = chunk_size, chunk_overlap = chunk_overlap, length_function = len, separators=["\n# "])
        elif chunk_method == "NLTK":
            text_splitter = NLTKTextSplitter(chunk_size=chunk_size, chunk_overlap = chunk_overlap, length_function = len)
        elif chunk_method == "Spacy":
            text_splitter = SpacyTextSplitter(chunk_size=chunk_size, chunk_overlap = chunk_overlap, length_function = len, pipeline="zh_core_web_sm", max_length=2500000)
        elif chunk_method == "SingleChunk":
            pass
        else:
            print ("Error in choosing chunk method")

        # TODO：如果chunk内容过大，超过100万字符，不进行chunking。未来考虑是否需要分块后再进行chunking
        if len(new_chunk_content) > 1000000:
            print ("Chunk content too large, skip chunking")
            return chunked_docs

        if chunk_method == "NLTK" or chunk_method == "SingleChunk":
            chunks = [new_chunk_content]
        else: 
            chunks = text_splitter.split_text(new_chunk_content)
    
    
    for i, chunk in enumerate(chunks):
        doc = Document(
            page_content=chunk.strip(),
            metadata={
                "type" : chunk_type,
                "agent_id": agent_id,
                "agent_name": agent_name,
                "doc_id": doc_id,
                "doc_id_parent": doc_id_parent,
                "source_id" : chunk_id,
                "source_id_parent": chunk_id_parent,
                "source" : chunk_name,
                "category" : chunk_category,
                "dirId" : dir_id,
                "chunk" : i + 1,
                "faq_answer": chunk_content.strip() if chunk_type == "cskb_faq" else ""
            },
        )
        chunked_docs.append(doc)
    
    return chunked_docs


# 百度知识库文档文本分割
def chunk_base_doc(knowledge_field, doc_dict, chunk_size: int, chunk_method:str, chunk_overlap: int = 0, type:str = "doc"):
    doc_name = doc_dict['title']
    print (f'Chunking doc "{doc_name}" in {chunk_method} mode')

    chunk_type = "cskb_attach" if type == "attach" else "cskb_doc"

    chunked_docs = split_chunks(
        chunk_type = chunk_type,
        chunk_method = chunk_method,
        agent_id = doc_dict['agentId'],
        agent_name = doc_dict['agentName'],
        dir_id = doc_dict['dirId'],
        doc_id = doc_dict['id'],
        doc_id_parent = doc_dict['id_parent'],
        chunk_name = doc_name,
        chunk_id = doc_dict['originalId'],
        chunk_id_parent = doc_dict['originalId_parent'],
        chunk_size = chunk_size,
        chunk_overlap = chunk_overlap,
        chunk_content = doc_dict['docText'],
        chunk_category = doc_dict['dir_name'],
    )
    for doc in chunked_docs:
        print('doc', doc)
        print('doc', doc.metadata)
        upload_knowledge(database=knowledge_field, collection='doc', texts=[doc.page_content], metadata=doc.metadata)
        return 
    
    print (f'Chunked doc "{doc_name}" in {chunk_method} mode with chunks: {len(chunked_docs)}')

    all_chunks = []
    for doc in chunked_docs:
        all_chunks.append(doc)

    print('----------------------------------------------')
    print('all_chunks', all_chunks)
    print('**********************************************')
    return
  


if __name__ == '__main__':
    data = {'id': 'c2_8a77f2a5c5e15071301b69f0e5cfb5c5', 'agentId': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agentName': '零号员工知识库', 'question': '部门如何进行会务接待礼仪服务申请？会务礼仪申请？', 'answer': '1.公司及以上级别的会议、接待、大型活动中，主办方如有会务服务需求，可申请会务服务。2.会务服务包含会场布置、茶水服务、会议所涉及的礼仪服务等。3.主办部门提前一周填写《HSC会务接待礼仪服务申请表》，完成审批流转后，联系HSC俞华695-52665，或IMB会务52653进行具体需求的对接。', 'dirId': 'a0f9bb4bab23c16a41ada082883b5cb0', 'originalId': 'a2_2f2414f00ee96dbf877cd65e61d4ce0f', 'pubUserName': '曹阳_67841', 'pubTime': '2025-03-31 11:02:39', 'versionStatus': 1, 'dir_name': '[零号员工知识库] HS-安全管理知识', 'dir_level': 1}
    upload_knowledge(data)